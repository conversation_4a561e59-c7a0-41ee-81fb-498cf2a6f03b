<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PRANA - Fizyoterapi & Pilates Salonu</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
            color: #333;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            z-index: 1000;
            padding: 1rem 0;
            transition: all 0.3s ease;
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .logo {
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: #667eea;
        }

        /* Hero Section */
        .hero {
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="1" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }

        .hero-content {
            z-index: 2;
            max-width: 800px;
            padding: 0 2rem;
        }

        .hero h1 {
            font-size: 4rem;
            font-weight: 700;
            margin-bottom: 1rem;
            opacity: 0;
            animation: fadeInUp 1s ease 0.5s forwards;
        }

        .hero p {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0;
            animation: fadeInUp 1s ease 0.8s forwards;
        }

        .cta-button {
            display: inline-block;
            padding: 1rem 2rem;
            background: white;
            color: #667eea;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            opacity: 0;
            animation: fadeInUp 1s ease 1.1s forwards;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        /* Scroll indicator */
        .scroll-indicator {
            position: absolute;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            animation: bounce 2s infinite;
        }

        /* About Section */
        .about {
            padding: 5rem 0;
            background: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .section-title {
            text-align: center;
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 3rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .about-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
        }

        .about-text {
            font-size: 1.1rem;
            line-height: 1.8;
        }

        .about-image {
            text-align: center;
        }

        .about-image i {
            font-size: 15rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: pulse 3s ease-in-out infinite;
        }

        /* Pilates Benefits */
        .benefits {
            padding: 5rem 0;
            background: white;
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .benefit-card {
            background: white;
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
            transition: all 0.3s ease;
            opacity: 0;
            transform: translateY(50px);
        }

        .benefit-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .benefit-card.animate {
            opacity: 1;
            transform: translateY(0);
        }

        .benefit-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .benefit-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #333;
        }

        /* Who Can Do */
        .who-can-do {
            padding: 5rem 0;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }

        .personas-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .persona-card {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            padding: 2rem;
            border-radius: 20px;
            text-align: center;
            transition: all 0.3s ease;
            opacity: 0;
            transform: scale(0.8);
        }

        .persona-card.animate {
            opacity: 1;
            transform: scale(1);
        }

        .persona-card:hover {
            transform: scale(1.05);
        }

        .persona-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }

        /* Services */
        .services {
            padding: 5rem 0;
            background: #f8f9fa;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .service-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            opacity: 0;
            transform: translateX(-50px);
        }

        .service-card.animate {
            opacity: 1;
            transform: translateX(0);
        }

        .service-card:hover {
            transform: translateY(-10px);
        }

        .service-header {
            padding: 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }

        .service-body {
            padding: 2rem;
        }

        /* Contact */
        .contact {
            padding: 5rem 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }

        .contact-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .contact-item {
            padding: 2rem;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            transition: all 0.3s ease;
        }

        .contact-item:hover {
            transform: translateY(-5px);
        }

        .contact-item i {
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateX(-50%) translateY(0);
            }
            40% {
                transform: translateX(-50%) translateY(-10px);
            }
            60% {
                transform: translateX(-50%) translateY(-5px);
            }
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-20px);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .about-grid {
                grid-template-columns: 1fr;
                text-align: center;
            }
            
            .nav-links {
                display: none;
            }
            
            .section-title {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav">
            <div class="logo">PRANA</div>
            <ul class="nav-links">
                <li><a href="#home">Ana Sayfa</a></li>
                <li><a href="#about">Hakkımızda</a></li>
                <li><a href="#benefits">Faydalar</a></li>
                <li><a href="#services">Hizmetler</a></li>
                <li><a href="#contact">İletişim</a></li>
            </ul>
        </nav>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-content">
            <h1>PRANA</h1>
            <p>Fizyoterapi ve Pilates ile Yaşamınızı Dönüştürün</p>
            <a href="#about" class="cta-button">Keşfet</a>
        </div>
        <div class="scroll-indicator">
            <i class="fas fa-chevron-down"></i>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about">
        <div class="container">
            <h2 class="section-title">Pilates Nedir?</h2>
            <div class="about-grid">
                <div class="about-text">
                    <p>Pilates, 20. yüzyılın başlarında Joseph Pilates tarafından geliştirilen, vücut ve zihin uyumunu hedefleyen bir egzersiz sistemidir. Bu yöntem, güçlü bir merkez (core) oluşturmaya, esnekliği artırmaya ve postürü düzeltmeye odaklanır.</p>
                    <br>
                    <p>PRANA'da, deneyimli fizyoterapistlerimiz ve pilates eğitmenlerimiz ile birlikte, kişisel ihtiyaçlarınıza özel programlar sunuyoruz. Her yaştan ve her seviyeden kişi için uygun olan pilates, yaşam kalitenizi artıracak.</p>
                </div>
                <div class="about-image">
                    <i class="fas fa-spa"></i>
                </div>
            </div>
        </div>
    </section>

    <!-- Benefits Section -->
    <section id="benefits" class="benefits">
        <div class="container">
            <h2 class="section-title">Pilatesin Faydaları</h2>
            <div class="benefits-grid">
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-dumbbell"></i>
                    </div>
                    <h3>Güç ve Dayanıklılık</h3>
                    <p>Core kaslarınızı güçlendirerek genel vücut gücünüzü artırır ve günlük aktivitelerinizde daha dayanıklı olmanızı sağlar.</p>
                </div>
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </div>
                    <h3>Esneklik</h3>
                    <p>Kaslarınızın uzunluğunu artırarak esnekliğinizi geliştirir ve hareket kabiliyetinizi genişletir.</p>
                </div>
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-balance-scale"></i>
                    </div>
                    <h3>Denge ve Koordinasyon</h3>
                    <p>Vücut farkındalığınızı artırarak denge ve koordinasyonunuzu geliştirir, yaralanma riskini azaltır.</p>
                </div>
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <h3>Postür Düzeltme</h3>
                    <p>Yanlış duruş alışkanlıklarını düzelterek sırt ağrılarını azaltır ve daha dik bir duruş sağlar.</p>
                </div>
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3>Zihinsel Rahatlama</h3>
                    <p>Nefes teknikleri ve konsantrasyon ile stresi azaltır, zihinsel berraklık sağlar.</p>
                </div>
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h3>Genel Sağlık</h3>
                    <p>Kan dolaşımını iyileştirir, enerji seviyenizi artırır ve genel yaşam kalitenizi yükseltir.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Who Can Do Section -->
    <section id="who-can-do" class="who-can-do">
        <div class="container">
            <h2 class="section-title">Kimler Pilates Yapabilir?</h2>
            <div class="personas-grid">
                <div class="persona-card">
                    <div class="persona-icon">
                        <i class="fas fa-user-graduate"></i>
                    </div>
                    <h3>Yeni Başlayanlar</h3>
                    <p>Hiç spor yapmamış olsanız bile, temel seviyeden başlayarak güvenli bir şekilde pilates yapabilirsiniz.</p>
                </div>
                <div class="persona-card">
                    <div class="persona-icon">
                        <i class="fas fa-running"></i>
                    </div>
                    <h3>Sporcular</h3>
                    <p>Performansınızı artırmak ve yaralanmaları önlemek için pilates mükemmel bir tamamlayıcı egzersizdir.</p>
                </div>
                <div class="persona-card">
                    <div class="persona-icon">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <h3>Ofis Çalışanları</h3>
                    <p>Uzun saatler masa başında çalışmanın getirdiği postür problemlerini çözmek için idealdir.</p>
                </div>
                <div class="persona-card">
                    <div class="persona-icon">
                        <i class="fas fa-female"></i>
                    </div>
                    <h3>Hamile Kadınlar</h3>
                    <p>Özel prenatal pilates programları ile hamilelik sürecinde sağlıklı kalabilirsiniz.</p>
                </div>
                <div class="persona-card">
                    <div class="persona-icon">
                        <i class="fas fa-user-clock"></i>
                    </div>
                    <h3>Yaşlılar</h3>
                    <p>Düşük etkili hareketlerle eklem sağlığınızı koruyarak aktif yaşlanma sağlayabilirsiniz.</p>
                </div>
                <div class="persona-card">
                    <div class="persona-icon">
                        <i class="fas fa-user-injured"></i>
                    </div>
                    <h3>Rehabilitasyon</h3>
                    <p>Yaralanma sonrası iyileşme sürecinde fizyoterapist eşliğinde güvenli egzersiz yapabilirsiniz.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="services">
        <div class="container">
            <h2 class="section-title">Hizmetlerimiz</h2>
            <div class="services-grid">
                <div class="service-card">
                    <div class="service-header">
                        <h3>Bireysel Pilates</h3>
                        <i class="fas fa-user" style="font-size: 2rem; margin-top: 1rem;"></i>
                    </div>
                    <div class="service-body">
                        <p>Kişisel ihtiyaçlarınıza özel tasarlanmış birebir pilates seansları. Deneyimli eğitmenlerimiz ile hızlı ve etkili sonuçlar alın.</p>
                        <ul style="margin-top: 1rem; text-align: left;">
                            <li>• Kişisel değerlendirme</li>
                            <li>• Özel program tasarımı</li>
                            <li>• Birebir takip</li>
                        </ul>
                    </div>
                </div>
                <div class="service-card">
                    <div class="service-header">
                        <h3>Grup Dersleri</h3>
                        <i class="fas fa-users" style="font-size: 2rem; margin-top: 1rem;"></i>
                    </div>
                    <div class="service-body">
                        <p>Sosyal bir ortamda motivasyonunuzu artırarak pilates yapın. Farklı seviyeler için uygun grup dersleri.</p>
                        <ul style="margin-top: 1rem; text-align: left;">
                            <li>• Başlangıç seviyesi</li>
                            <li>• Orta seviye</li>
                            <li>• İleri seviye</li>
                        </ul>
                    </div>
                </div>
                <div class="service-card">
                    <div class="service-header">
                        <h3>Fizyoterapi</h3>
                        <i class="fas fa-hand-holding-medical" style="font-size: 2rem; margin-top: 1rem;"></i>
                    </div>
                    <div class="service-body">
                        <p>Lisanslı fizyoterapistlerimiz ile ağrı tedavisi, rehabilitasyon ve hareket kısıtlılığı problemlerinizi çözün.</p>
                        <ul style="margin-top: 1rem; text-align: left;">
                            <li>• Manuel terapi</li>
                            <li>• Egzersiz tedavisi</li>
                            <li>• Postür analizi</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact">
        <div class="container">
            <h2 class="section-title">İletişim</h2>
            <p style="font-size: 1.2rem; margin-bottom: 2rem;">Sağlıklı yaşamınıza ilk adımı atmak için bizimle iletişime geçin!</p>
            <div class="contact-info">
                <div class="contact-item">
                    <i class="fas fa-phone"></i>
                    <h3>Telefon</h3>
                    <p>+90 (*************</p>
                </div>
                <div class="contact-item">
                    <i class="fas fa-envelope"></i>
                    <h3>E-posta</h3>
                    <p><EMAIL></p>
                </div>
                <div class="contact-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <h3>Adres</h3>
                    <p>Nişantaşı, İstanbul<br>Pilates & Fizyoterapi Merkezi</p>
                </div>
                <div class="contact-item">
                    <i class="fas fa-clock"></i>
                    <h3>Çalışma Saatleri</h3>
                    <p>Pazartesi - Cuma: 08:00 - 20:00<br>Cumartesi: 09:00 - 17:00</p>
                </div>
            </div>
        </div>
    </section>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Header background on scroll
        window.addEventListener('scroll', () => {
            const header = document.querySelector('.header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.98)';
                header.style.boxShadow = '0 2px 20px rgba(0,0,0,0.1)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
                header.style.boxShadow = 'none';
            }
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate');
                }
            });
        }, observerOptions);

        // Observe benefit cards
        document.querySelectorAll('.benefit-card').forEach((card, index) => {
            card.style.transitionDelay = `${index * 0.1}s`;
            observer.observe(card);
        });

        // Observe persona cards
        document.querySelectorAll('.persona-card').forEach((card, index) => {
            card.style.transitionDelay = `${index * 0.15}s`;
            observer.observe(card);
        });

        // Observe service cards
        document.querySelectorAll('.service-card').forEach((card, index) => {
            card.style.transitionDelay = `${index * 0.2}s`;
            observer.observe(card);
        });

        // Parallax effect for hero section
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const hero = document.querySelector('.hero');
            const rate = scrolled * -0.5;
            hero.style.transform = `translateY(${rate}px)`;
        });

        // Add floating animation to benefit icons
        document.querySelectorAll('.benefit-icon i').forEach((icon, index) => {
            icon.style.animationDelay = `${index * 0.5}s`;
            icon.style.animation = 'float 3s ease-in-out infinite';
        });

        // Add hover effect to navigation
        document.querySelectorAll('.nav-links a').forEach(link => {
            link.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });
            link.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    </script>
</body>
</html>
