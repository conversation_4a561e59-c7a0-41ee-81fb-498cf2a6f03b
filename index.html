<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Primary Meta Tags -->
    <title>PRANA Fizyoterapi & Pilates Salonu | İstanbul Nişantaşı | Profesyonel Pilates Dersleri</title>
    <meta name="title" content="PRANA Fizyoterapi & Pilates Salonu | İstanbul Nişantaşı | Profesyonel Pilates Dersleri">
    <meta name="description" content="İstanbul Nişantaşı'nda bulunan PRANA Fizyoterapi & Pilates Salonu. Bireysel pilates, grup dersleri ve fizyoterapi hizmetleri. <PERSON><PERSON><PERSON><PERSON>, modern ekipmanlar. Randevu için hemen arayın!">
    <meta name="keywords" content="pilates istanbul, fizyoterapi istanbul, pilates nişantaşı, pilates dersleri, bireysel pilates, grup pilates, fizyoterapi merkezi, post<PERSON><PERSON>, core güçlendirme, rehabilitasyon, PRANA pilates">
    <meta name="author" content="PRANA Fizyoterapi & Pilates Salonu">
    <meta name="robots" content="index, follow">
    <meta name="language" content="Turkish">
    <meta name="revisit-after" content="7 days">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://www.pranastudio.com/">
    <meta property="og:title" content="PRANA Fizyoterapi & Pilates Salonu | İstanbul Nişantaşı">
    <meta property="og:description" content="İstanbul'un en iyi pilates ve fizyoterapi merkezi. Profesyonel eğitmenler, kişisel programlar ve modern ekipmanlarla sağlıklı yaşamınıza başlayın.">
    <meta property="og:image" content="https://www.pranastudio.com/images/prana-pilates-og.jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:locale" content="tr_TR">
    <meta property="og:site_name" content="PRANA Pilates & Fizyoterapi">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://www.pranastudio.com/">
    <meta property="twitter:title" content="PRANA Fizyoterapi & Pilates Salonu | İstanbul Nişantaşı">
    <meta property="twitter:description" content="İstanbul'un en iyi pilates ve fizyoterapi merkezi. Profesyonel eğitmenler, kişisel programlar ve modern ekipmanlarla sağlıklı yaşamınıza başlayın.">
    <meta property="twitter:image" content="https://www.pranastudio.com/images/prana-pilates-twitter.jpg">

    <!-- Canonical URL -->
    <link rel="canonical" href="https://www.pranastudio.com/">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">

    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">

    <!-- Fonts and CSS -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Structured Data - Local Business -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "HealthAndBeautyBusiness",
        "name": "PRANA Fizyoterapi & Pilates Salonu",
        "image": "https://www.pranastudio.com/images/prana-logo.jpg",
        "description": "İstanbul Nişantaşı'nda bulunan profesyonel fizyoterapi ve pilates merkezi. Bireysel pilates dersleri, grup dersleri ve fizyoterapi hizmetleri sunuyoruz.",
        "address": {
            "@type": "PostalAddress",
            "streetAddress": "Nişantaşı",
            "addressLocality": "İstanbul",
            "addressCountry": "TR"
        },
        "telephone": "+90-************",
        "email": "<EMAIL>",
        "url": "https://www.pranastudio.com",
        "openingHours": [
            "Mo-Fr 08:00-20:00",
            "Sa 09:00-17:00"
        ],
        "priceRange": "$$",
        "hasOfferCatalog": {
            "@type": "OfferCatalog",
            "name": "Pilates ve Fizyoterapi Hizmetleri",
            "itemListElement": [
                {
                    "@type": "Offer",
                    "itemOffered": {
                        "@type": "Service",
                        "name": "Bireysel Pilates Dersleri",
                        "description": "Kişisel ihtiyaçlarınıza özel tasarlanmış birebir pilates seansları"
                    }
                },
                {
                    "@type": "Offer",
                    "itemOffered": {
                        "@type": "Service",
                        "name": "Grup Pilates Dersleri",
                        "description": "Farklı seviyeler için uygun grup pilates dersleri"
                    }
                },
                {
                    "@type": "Offer",
                    "itemOffered": {
                        "@type": "Service",
                        "name": "Fizyoterapi",
                        "description": "Lisanslı fizyoterapistler ile ağrı tedavisi ve rehabilitasyon"
                    }
                }
            ]
        },
        "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.9",
            "reviewCount": "127"
        }
    }
    </script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
            color: #333;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            z-index: 1000;
            padding: 1rem 0;
            transition: all 0.3s ease;
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .logo {
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: #667eea;
        }

        /* Hero Section */
        .hero {
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="1" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }

        .hero-content {
            z-index: 2;
            max-width: 800px;
            padding: 0 2rem;
        }

        .hero h1 {
            font-size: 4rem;
            font-weight: 700;
            margin-bottom: 1rem;
            opacity: 0;
            animation: fadeInUp 1s ease 0.5s forwards;
        }

        .hero p {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0;
            animation: fadeInUp 1s ease 0.8s forwards;
        }

        .cta-button {
            display: inline-block;
            padding: 1rem 2rem;
            background: white;
            color: #667eea;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            opacity: 0;
            animation: fadeInUp 1s ease 1.1s forwards;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        /* Scroll indicator */
        .scroll-indicator {
            position: absolute;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            animation: bounce 2s infinite;
        }

        /* About Section */
        .about {
            padding: 5rem 0;
            background: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .section-title {
            text-align: center;
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 3rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .about-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
        }

        .about-text {
            font-size: 1.1rem;
            line-height: 1.8;
        }

        .about-image {
            text-align: center;
        }

        .about-image i {
            font-size: 15rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: pulse 3s ease-in-out infinite;
        }

        /* Pilates Benefits */
        .benefits {
            padding: 5rem 0;
            background: white;
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .benefit-card {
            background: white;
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
            transition: all 0.3s ease;
            opacity: 0;
            transform: translateY(50px);
        }

        .benefit-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .benefit-card.animate {
            opacity: 1;
            transform: translateY(0);
        }

        .benefit-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .benefit-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #333;
        }

        /* Who Can Do */
        .who-can-do {
            padding: 5rem 0;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }

        .personas-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .persona-card {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            padding: 2rem;
            border-radius: 20px;
            text-align: center;
            transition: all 0.3s ease;
            opacity: 0;
            transform: scale(0.8);
        }

        .persona-card.animate {
            opacity: 1;
            transform: scale(1);
        }

        .persona-card:hover {
            transform: scale(1.05);
        }

        .persona-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }

        /* Services */
        .services {
            padding: 5rem 0;
            background: #f8f9fa;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .service-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            opacity: 0;
            transform: translateX(-50px);
        }

        .service-card.animate {
            opacity: 1;
            transform: translateX(0);
        }

        .service-card:hover {
            transform: translateY(-10px);
        }

        .service-header {
            padding: 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }

        .service-body {
            padding: 2rem;
        }

        /* Contact */
        .contact {
            padding: 5rem 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }

        .contact-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .contact-item {
            padding: 2rem;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            transition: all 0.3s ease;
        }

        .contact-item:hover {
            transform: translateY(-5px);
        }

        .contact-item i {
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateX(-50%) translateY(0);
            }
            40% {
                transform: translateX(-50%) translateY(-10px);
            }
            60% {
                transform: translateX(-50%) translateY(-5px);
            }
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-20px);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .about-grid {
                grid-template-columns: 1fr;
                text-align: center;
            }
            
            .nav-links {
                display: none;
            }
            
            .section-title {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav">
            <div class="logo">PRANA</div>
            <ul class="nav-links">
                <li><a href="#home">Ana Sayfa</a></li>
                <li><a href="#about">Hakkımızda</a></li>
                <li><a href="#benefits">Faydalar</a></li>
                <li><a href="#services">Hizmetler</a></li>
                <li><a href="#contact">İletişim</a></li>
            </ul>
        </nav>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-content">
            <h1>PRANA Fizyoterapi & Pilates Salonu İstanbul</h1>
            <p>Nişantaşı'nda Profesyonel Fizyoterapi ve Pilates Dersleri ile Yaşamınızı Dönüştürün</p>
            <a href="#about" class="cta-button" aria-label="Pilates hakkında daha fazla bilgi edinin">Keşfet</a>
        </div>
        <div class="scroll-indicator" aria-label="Aşağı kaydır">
            <i class="fas fa-chevron-down"></i>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about">
        <div class="container">
            <h2 class="section-title">İstanbul'da Pilates Nedir? Nasıl Yapılır?</h2>
            <div class="about-grid">
                <div class="about-text">
                    <p><strong>Pilates</strong>, 20. yüzyılın başlarında Joseph Pilates tarafından geliştirilen, vücut ve zihin uyumunu hedefleyen bir <em>egzersiz sistemi</em>dir. Bu yöntem, güçlü bir merkez (<strong>core</strong>) oluşturmaya, esnekliği artırmaya ve <strong>postür düzeltmeye</strong> odaklanır.</p>
                    <br>
                    <p><strong>PRANA Pilates Salonu</strong>'nda, deneyimli <em>fizyoterapistlerimiz</em> ve <em>pilates eğitmenlerimiz</em> ile birlikte, kişisel ihtiyaçlarınıza özel programlar sunuyoruz. Her yaştan ve her seviyeden kişi için uygun olan <strong>pilates dersleri</strong>, yaşam kalitenizi artıracak.</p>
                </div>
                <div class="about-image">
                    <i class="fas fa-spa" alt="Pilates ve wellness ikonu"></i>
                </div>
            </div>
        </div>
    </section>

    <!-- Benefits Section -->
    <section id="benefits" class="benefits">
        <div class="container">
            <h2 class="section-title">Pilates Yapmanın 6 Önemli Faydası</h2>
            <div class="benefits-grid">
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-dumbbell"></i>
                    </div>
                    <h3>Güç ve Dayanıklılık</h3>
                    <p>Core kaslarınızı güçlendirerek genel vücut gücünüzü artırır ve günlük aktivitelerinizde daha dayanıklı olmanızı sağlar.</p>
                </div>
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </div>
                    <h3>Esneklik</h3>
                    <p>Kaslarınızın uzunluğunu artırarak esnekliğinizi geliştirir ve hareket kabiliyetinizi genişletir.</p>
                </div>
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-balance-scale"></i>
                    </div>
                    <h3>Denge ve Koordinasyon</h3>
                    <p>Vücut farkındalığınızı artırarak denge ve koordinasyonunuzu geliştirir, yaralanma riskini azaltır.</p>
                </div>
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <h3>Postür Düzeltme</h3>
                    <p>Yanlış duruş alışkanlıklarını düzelterek sırt ağrılarını azaltır ve daha dik bir duruş sağlar.</p>
                </div>
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3>Zihinsel Rahatlama</h3>
                    <p>Nefes teknikleri ve konsantrasyon ile stresi azaltır, zihinsel berraklık sağlar.</p>
                </div>
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h3>Genel Sağlık</h3>
                    <p>Kan dolaşımını iyileştirir, enerji seviyenizi artırır ve genel yaşam kalitenizi yükseltir.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Who Can Do Section -->
    <section id="who-can-do" class="who-can-do">
        <div class="container">
            <h2 class="section-title">Hangi Yaş Grupları Pilates Yapabilir?</h2>
            <div class="personas-grid">
                <div class="persona-card">
                    <div class="persona-icon">
                        <i class="fas fa-user-graduate"></i>
                    </div>
                    <h3>Yeni Başlayanlar</h3>
                    <p>Hiç spor yapmamış olsanız bile, temel seviyeden başlayarak güvenli bir şekilde pilates yapabilirsiniz.</p>
                </div>
                <div class="persona-card">
                    <div class="persona-icon">
                        <i class="fas fa-running"></i>
                    </div>
                    <h3>Sporcular</h3>
                    <p>Performansınızı artırmak ve yaralanmaları önlemek için pilates mükemmel bir tamamlayıcı egzersizdir.</p>
                </div>
                <div class="persona-card">
                    <div class="persona-icon">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <h3>Ofis Çalışanları</h3>
                    <p>Uzun saatler masa başında çalışmanın getirdiği postür problemlerini çözmek için idealdir.</p>
                </div>
                <div class="persona-card">
                    <div class="persona-icon">
                        <i class="fas fa-female"></i>
                    </div>
                    <h3>Hamile Kadınlar</h3>
                    <p>Özel prenatal pilates programları ile hamilelik sürecinde sağlıklı kalabilirsiniz.</p>
                </div>
                <div class="persona-card">
                    <div class="persona-icon">
                        <i class="fas fa-user-clock"></i>
                    </div>
                    <h3>Yaşlılar</h3>
                    <p>Düşük etkili hareketlerle eklem sağlığınızı koruyarak aktif yaşlanma sağlayabilirsiniz.</p>
                </div>
                <div class="persona-card">
                    <div class="persona-icon">
                        <i class="fas fa-user-injured"></i>
                    </div>
                    <h3>Rehabilitasyon</h3>
                    <p>Yaralanma sonrası iyileşme sürecinde fizyoterapist eşliğinde güvenli egzersiz yapabilirsiniz.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="services">
        <div class="container">
            <h2 class="section-title">PRANA Pilates & Fizyoterapi Hizmetlerimiz</h2>
            <div class="services-grid">
                <div class="service-card">
                    <div class="service-header">
                        <h3>Bireysel Pilates</h3>
                        <i class="fas fa-user" style="font-size: 2rem; margin-top: 1rem;"></i>
                    </div>
                    <div class="service-body">
                        <p>Kişisel ihtiyaçlarınıza özel tasarlanmış birebir pilates seansları. Deneyimli eğitmenlerimiz ile hızlı ve etkili sonuçlar alın.</p>
                        <ul style="margin-top: 1rem; text-align: left;">
                            <li>• Kişisel değerlendirme</li>
                            <li>• Özel program tasarımı</li>
                            <li>• Birebir takip</li>
                        </ul>
                    </div>
                </div>
                <div class="service-card">
                    <div class="service-header">
                        <h3>Grup Dersleri</h3>
                        <i class="fas fa-users" style="font-size: 2rem; margin-top: 1rem;"></i>
                    </div>
                    <div class="service-body">
                        <p>Sosyal bir ortamda motivasyonunuzu artırarak pilates yapın. Farklı seviyeler için uygun grup dersleri.</p>
                        <ul style="margin-top: 1rem; text-align: left;">
                            <li>• Başlangıç seviyesi</li>
                            <li>• Orta seviye</li>
                            <li>• İleri seviye</li>
                        </ul>
                    </div>
                </div>
                <div class="service-card">
                    <div class="service-header">
                        <h3>Fizyoterapi</h3>
                        <i class="fas fa-hand-holding-medical" style="font-size: 2rem; margin-top: 1rem;"></i>
                    </div>
                    <div class="service-body">
                        <p>Lisanslı fizyoterapistlerimiz ile ağrı tedavisi, rehabilitasyon ve hareket kısıtlılığı problemlerinizi çözün.</p>
                        <ul style="margin-top: 1rem; text-align: left;">
                            <li>• Manuel terapi</li>
                            <li>• Egzersiz tedavisi</li>
                            <li>• Postür analizi</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact">
        <div class="container">
            <h2 class="section-title">İletişim - PRANA Pilates Salonu Nişantaşı</h2>
            <p style="font-size: 1.2rem; margin-bottom: 2rem;">Pilates dersleri ve fizyoterapi randevusu için <strong>hemen arayın</strong>! İstanbul'un en iyi pilates merkezi.</p>
            <div class="contact-info">
                <div class="contact-item">
                    <i class="fas fa-phone"></i>
                    <h3>Telefon</h3>
                    <p>+90 (*************</p>
                </div>
                <div class="contact-item">
                    <i class="fas fa-envelope"></i>
                    <h3>E-posta</h3>
                    <p><EMAIL></p>
                </div>
                <div class="contact-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <h3>Adres</h3>
                    <p>Nişantaşı, İstanbul<br>Pilates & Fizyoterapi Merkezi</p>
                </div>
                <div class="contact-item">
                    <i class="fas fa-clock"></i>
                    <h3>Çalışma Saatleri</h3>
                    <p>Pazartesi - Cuma: 08:00 - 20:00<br>Cumartesi: 09:00 - 17:00</p>
                </div>
            </div>
        </div>
    </section>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Header background on scroll
        window.addEventListener('scroll', () => {
            const header = document.querySelector('.header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.98)';
                header.style.boxShadow = '0 2px 20px rgba(0,0,0,0.1)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
                header.style.boxShadow = 'none';
            }
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate');
                }
            });
        }, observerOptions);

        // Observe benefit cards
        document.querySelectorAll('.benefit-card').forEach((card, index) => {
            card.style.transitionDelay = `${index * 0.1}s`;
            observer.observe(card);
        });

        // Observe persona cards
        document.querySelectorAll('.persona-card').forEach((card, index) => {
            card.style.transitionDelay = `${index * 0.15}s`;
            observer.observe(card);
        });

        // Observe service cards
        document.querySelectorAll('.service-card').forEach((card, index) => {
            card.style.transitionDelay = `${index * 0.2}s`;
            observer.observe(card);
        });

        // Parallax effect for hero section
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const hero = document.querySelector('.hero');
            const rate = scrolled * -0.5;
            hero.style.transform = `translateY(${rate}px)`;
        });

        // Add floating animation to benefit icons
        document.querySelectorAll('.benefit-icon i').forEach((icon, index) => {
            icon.style.animationDelay = `${index * 0.5}s`;
            icon.style.animation = 'float 3s ease-in-out infinite';
        });

        // Add hover effect to navigation
        document.querySelectorAll('.nav-links a').forEach(link => {
            link.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });
            link.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });

        // Add schema markup for FAQ (if needed later)
        // Performance optimization
        window.addEventListener('load', function() {
            // Lazy load images if any added later
            const images = document.querySelectorAll('img[data-src]');
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });
            images.forEach(img => imageObserver.observe(img));
        });
    </script>

    <!-- Footer for SEO -->
    <footer style="background: #333; color: white; padding: 2rem 0; text-align: center;">
        <div class="container">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem; margin-bottom: 2rem;">
                <div>
                    <h3 style="color: #667eea; margin-bottom: 1rem;">PRANA Pilates & Fizyoterapi</h3>
                    <p>İstanbul Nişantaşı'nda bulunan profesyonel pilates ve fizyoterapi merkezi. Sağlıklı yaşamınız için buradayız.</p>
                </div>
                <div>
                    <h4 style="margin-bottom: 1rem;">Hızlı Linkler</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin-bottom: 0.5rem;"><a href="#about" style="color: #ccc; text-decoration: none;">Pilates Nedir</a></li>
                        <li style="margin-bottom: 0.5rem;"><a href="#benefits" style="color: #ccc; text-decoration: none;">Pilates Faydaları</a></li>
                        <li style="margin-bottom: 0.5rem;"><a href="#services" style="color: #ccc; text-decoration: none;">Hizmetlerimiz</a></li>
                        <li style="margin-bottom: 0.5rem;"><a href="#contact" style="color: #ccc; text-decoration: none;">İletişim</a></li>
                    </ul>
                </div>
                <div>
                    <h4 style="margin-bottom: 1rem;">Hizmetlerimiz</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin-bottom: 0.5rem;">• Bireysel Pilates Dersleri</li>
                        <li style="margin-bottom: 0.5rem;">• Grup Pilates Dersleri</li>
                        <li style="margin-bottom: 0.5rem;">• Fizyoterapi</li>
                        <li style="margin-bottom: 0.5rem;">• Postür Analizi</li>
                    </ul>
                </div>
                <div>
                    <h4 style="margin-bottom: 1rem;">Anahtar Kelimeler</h4>
                    <p style="font-size: 0.9rem; color: #ccc;">
                        Pilates İstanbul, Fizyoterapi Nişantaşı, Pilates Dersleri, Core Güçlendirme,
                        Postür Düzeltme, Rehabilitasyon, Bireysel Pilates, Grup Pilates,
                        Pilates Eğitmeni, Fizyoterapist
                    </p>
                </div>
            </div>
            <hr style="border: 1px solid #555; margin: 2rem 0;">
            <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;">
                <p>&copy; 2024 PRANA Fizyoterapi & Pilates Salonu. Tüm hakları saklıdır.</p>
                <div>
                    <span style="margin-right: 1rem;">İstanbul, Nişantaşı</span>
                    <span style="margin-right: 1rem;">Tel: +90 (*************</span>
                    <span><EMAIL></span>
                </div>
            </div>
        </div>
    </footer>

    <!-- Additional SEO Scripts -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "FAQPage",
        "mainEntity": [
            {
                "@type": "Question",
                "name": "Pilates nedir ve nasıl yapılır?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Pilates, Joseph Pilates tarafından geliştirilen, core kaslarını güçlendiren, esnekliği artıran ve postürü düzelten bir egzersiz sistemidir. Kontrollü hareketler ve nefes teknikleri ile yapılır."
                }
            },
            {
                "@type": "Question",
                "name": "Pilates kimler yapabilir?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Pilates her yaştan kişi tarafından yapılabilir. Yeni başlayanlar, sporcular, hamile kadınlar, yaşlılar ve rehabilitasyon sürecindeki kişiler için uygundur."
                }
            },
            {
                "@type": "Question",
                "name": "PRANA'da hangi hizmetler sunuluyor?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "PRANA'da bireysel pilates dersleri, grup pilates dersleri ve fizyoterapi hizmetleri sunulmaktadır. Deneyimli eğitmenler ve modern ekipmanlar ile hizmet verilmektedir."
                }
            }
        ]
    }
    </script>
</body>
</html>
